import { useState } from 'react';
import {
  type PmaEntry,
  type PmaFormData,
  type PmaFormState,
} from '../types/pma-types';

export function usePmaManagement() {
  const [state, setState] = useState<PmaFormState>({
    entries: [],
    currentForm: {},
  });

  const addEntry = (formData: PmaFormData) => {
    const newEntry: PmaEntry = {
      ...formData,
      id: `PMA-${Date.now()}`,
      status: 'ready',
      createdAt: new Date().toISOString(),
    };

    setState((prev) => ({
      ...prev,
      entries: [...prev.entries, newEntry],
      currentForm: {},
    }));
  };

  const updateEntry = (id: string, formData: PmaFormData) => {
    setState((prev) => ({
      ...prev,
      entries: prev.entries.map((entry) =>
        entry.id === id
          ? { ...entry, ...formData, status: 'ready' as const }
          : entry,
      ),
      currentForm: {},
    }));
  };

  const deleteEntry = (id: string) => {
    setState((prev) => ({
      ...prev,
      entries: prev.entries.filter((entry) => entry.id !== id),
    }));
  };

  const setCurrentForm = (formData: Partial<PmaFormData>) => {
    setState((prev) => ({
      ...prev,
      currentForm: formData,
    }));
  };

  const resetCurrentForm = () => {
    setState((prev) => ({
      ...prev,
      currentForm: {},
    }));
  };

  const saveDraft = () => {
    // Implementation for saving draft
    console.log('Saving draft...');
  };

  const submitAllEntries = () => {
    // Implementation for submitting all entries
    console.log('Submitting all entries...', state.entries);
  };

  return {
    entries: state.entries,
    currentForm: state.currentForm,
    addEntry,
    updateEntry,
    deleteEntry,
    setCurrentForm,
    resetCurrentForm,
    saveDraft,
    submitAllEntries,
  };
}
