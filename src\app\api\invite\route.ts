import { createClient } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';

// Create admin client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!, // This requires service role key
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  },
);

interface InviteRequest {
  email: string;
  projectId: string;
  role: 'technician' | 'competent_person' | 'admin' | 'viewer';
  inviterId: string;
}

export async function POST(request: NextRequest) {
  try {
    // Get the current user session to verify authentication
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 },
      );
    }

    const token = authHeader.split(' ')[1];

    // Verify the user token using admin client
    const { data: user, error: authError } =
      await supabaseAdmin.auth.getUser(token);
    if (authError || !user.user) {
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 },
      );
    }

    const body = (await request.json()) as InviteRequest;
    const { email, projectId, role, inviterId } = body;

    // Validate input
    if (!email || !projectId || !role || !inviterId) {
      return NextResponse.json(
        { error: 'Missing required fields: email, projectId, role, inviterId' },
        { status: 400 },
      );
    }

    // Verify the user has permission to invite to this project
    const { data: projectUser, error: permissionError } = await supabaseAdmin
      .from('project_users')
      .select('role, is_active')
      .eq('project_id', projectId)
      .eq('user_id', user.user.id)
      .eq('is_active', true)
      .single();

    if (permissionError || !projectUser) {
      return NextResponse.json(
        {
          error: `You do not have permission to invite users to this project. Debug: userId=${user.user.id}, projectId=${projectId}`,
        },
        { status: 403 },
      );
    }

    // Check for existing pending invitation
    const { data: existingInvite } = await supabaseAdmin
      .from('project_invitations')
      .select('id')
      .eq('invitee_email', email.toLowerCase().trim())
      .eq('project_id', projectId)
      .eq('status', 'pending')
      .gt('expiry_date', new Date().toISOString())
      .maybeSingle();

    if (existingInvite) {
      return NextResponse.json(
        { error: 'An active invitation already exists for this email' },
        { status: 409 },
      );
    }

    // Get project and inviter details for email template
    const [projectResult, inviterResult] = await Promise.all([
      supabaseAdmin
        .from('projects')
        .select('name')
        .eq('id', projectId)
        .single(),
      supabaseAdmin.from('users').select('name').eq('id', inviterId).single(),
    ]);

    if (projectResult.error || inviterResult.error) {
      return NextResponse.json(
        { error: 'Failed to get project or inviter details' },
        { status: 500 },
      );
    }

    // Generate tokens
    const projectToken = crypto.randomUUID();
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + 7); // 7 days expiry

    // Generate redirect URL with default locale - redirect to auth callback first
    const origin = process.env.NEXT_PUBLIC_SITE_URL!;
    const redirectTo = `${origin}/en/set-password?project=${projectToken}`;

    // Send invitation using Supabase Admin Auth
    const { data: authData, error: inviteError } =
      await supabaseAdmin.auth.admin.inviteUserByEmail(email, {
        redirectTo,
        data: {
          projectName: projectResult.data.name,
          inviterName: inviterResult.data.name,
          role: role,
        },
      });

    if (inviteError) {
      console.error('Error sending Supabase invitation:', inviteError);
      return NextResponse.json(
        { error: `Failed to send invitation: ${inviteError.message}` },
        { status: 500 },
      );
    }

    if (!authData.user) {
      return NextResponse.json(
        { error: 'Failed to create user in Supabase Auth' },
        { status: 500 },
      );
    }

    // Create invitation record in database
    const { error: dbError } = await supabaseAdmin
      .from('project_invitations')
      .insert({
        project_id: projectId,
        inviter_user_id: inviterId,
        invitee_email: email.toLowerCase().trim(),
        role: role,
        token: projectToken,
        supabase_user_id: authData.user.id,
        status: 'pending',
        expiry_date: expiryDate.toISOString(),
        created_at: new Date().toISOString(),
      });

    if (dbError) {
      console.error('Error creating invitation record:', dbError);
      return NextResponse.json(
        { error: 'Failed to create invitation record' },
        { status: 500 },
      );
    }

    return NextResponse.json({
      success: true,
      supabaseUserId: authData.user.id,
      message: 'Invitation sent successfully',
    });
  } catch (error) {
    console.error('Error in invite API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
