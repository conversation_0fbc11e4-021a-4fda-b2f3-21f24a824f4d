import type { UserRole } from '@/types/auth';
import type {
  Action,
  Permission,
  Resource,
  RoutePermission,
} from '@/types/rbac';

// Role-based permissions mapping for Lift Management System
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  admin: [
    // Admin (formerly JKR_PIC and JKR_Admin) - Administrative access
    // Access scope is controlled by admin_access_mode (state/project) and monitoring_state
    'projects.view',
    'projects.create',
    'projects.edit',
    'analytics.view',
    'reports.view',
    'reports.create',
    'reports.manage',
    'profile.view',
    'profile.edit',
    'settings.view',
    'settings.manage',

    // Lift Management
    'lifts.view',
    'lifts.create',
    'lifts.edit',
    'lifts.delete',
    'lifts.assign',

    // Building Management
    'buildings.view',
    'buildings.create',
    'buildings.edit',
    'buildings.delete',

    // Contractor Management
    'contractors.view',
    'contractors.create',
    'contractors.edit',
    'contractors.delete',
    'contractors.activate',
    'contractors.deactivate',
    'contractors.blacklist',
    'contractors.approve',

    // Client Management
    'clients.view',
    'clients.create',
    'clients.edit',
    'clients.delete',

    // CP Directory
    'directory.view',
    'directory.create',
    'directory.edit',
    'directory.delete',
    'directory.approve',

    // Daily Logs
    'daily_logs.view',
    'daily_logs.create',
    'daily_logs.edit',
    'daily_logs.delete',

    // PMA Management
    'pmas.view',
    'pmas.create',
    'pmas.edit',
    'pmas.approve',
    'pmas.reject',

    // Complaint Management
    'complaints.view',
    'complaints.create',
    'complaints.edit',
    'complaints.resolve',
    'complaints.assign',

    // Blacklist Management
    'blacklist.view',
    'blacklist.create',
    'blacklist.edit',
    'blacklist.delete',

    // User Management
    'users.view',
    'users.create',
    'users.edit',
    'users.delete',
    'users.activate',
    'users.deactivate',
  ],
  contractor: [
    // Contractors - Can view and create projects, plus manage profile
    'projects.view',
    'projects.create',
    'profile.view',
    'profile.edit',

    // Lift Management - contractors can view lifts in their projects
    'lifts.view',

    // Building Management - contractors can view buildings in their projects
    'buildings.view',

    // Contract Management - contractors can view cp directory
    'directory.view',

    // Daily Logs - contractors can view and create daily logs
    'daily_logs.view',
    'daily_logs.create',
    'daily_logs.edit',

    // PMA Management - contractors can view and create PMAs
    'pmas.view',
    'pmas.create',
    'pmas.edit',

    // Complaint Management - contractors can view and manage complaints
    'complaints.view',
    'complaints.create',
    'complaints.edit',

    // Client Management - contractors can view clients
    'clients.view',

    // Analytics and Reports - contractors can view analytics and reports
    'analytics.view',
    'reports.view',
  ],
  viewer: [
    // Viewers (formerly Clients) - Building owners with limited access to daily logs and complaints only
    'profile.view',
    'profile.edit',

    // View daily logs for their lifts
    'daily_logs.view',

    // Submit and view complaints for their lifts
    'complaints.view',
    'complaints.create',
  ],
};

// Route-based permissions for Lift Management System
export const ROUTE_PERMISSIONS: RoutePermission[] = [
  // Core Pages
  {
    path: '/analytics',
    permission: 'analytics.view',
    roles: ['admin', 'contractor'],
  },
  {
    path: '/reports',
    permission: 'reports.view',
    roles: ['admin', 'contractor'],
  },
  {
    path: '/profile',
    permission: 'profile.view',
    roles: ['admin', 'contractor', 'viewer'],
  },
  { path: '/settings', permission: 'settings.view', roles: ['admin'] },

  // Project Management
  {
    path: '/projects',
    permission: 'projects.view',
    roles: ['admin', 'contractor'],
  },
  {
    path: '/projects/create',
    permission: 'projects.create',
    roles: ['admin', 'contractor'],
  },
  {
    path: '/projects/edit',
    permission: 'projects.edit',
    roles: ['admin'],
  },

  // Lift Management
  {
    path: '/lifts',
    permission: 'lifts.view',
    roles: ['admin', 'contractor'],
  },
  {
    path: '/lifts/create',
    permission: 'lifts.create',
    roles: ['admin'],
  },
  {
    path: '/lifts/edit',
    permission: 'lifts.edit',
    roles: ['admin'],
  },

  // Building Management
  {
    path: '/buildings',
    permission: 'buildings.view',
    roles: ['admin', 'contractor'],
  },
  {
    path: '/buildings/create',
    permission: 'buildings.create',
    roles: ['admin'],
  },
  {
    path: '/buildings/edit',
    permission: 'buildings.edit',
    roles: ['admin'],
  },

  // Contractor Management
  {
    path: '/contractors',
    permission: 'contractors.view',
    roles: ['admin'],
  },
  {
    path: '/contractors/create',
    permission: 'contractors.create',
    roles: ['admin'],
  },
  {
    path: '/contractors/edit',
    permission: 'contractors.edit',
    roles: ['admin'],
  },

  // Client Management
  {
    path: '/clients',
    permission: 'clients.view',
    roles: ['admin', 'contractor'],
  },
  {
    path: '/clients/create',
    permission: 'clients.create',
    roles: ['admin'],
  },
  {
    path: '/clients/edit',
    permission: 'clients.edit',
    roles: ['admin'],
  },

  // Contract Management
  {
    path: '/directory',
    permission: 'directory.view',
    roles: ['admin', 'contractor'],
  },
  {
    path: '/directory/create',
    permission: 'directory.create',
    roles: ['admin'],
  },
  {
    path: '/directory/edit',
    permission: 'directory.edit',
    roles: ['admin'],
  },

  // Daily Logs
  {
    path: '/daily-logs',
    permission: 'daily_logs.view',
    roles: ['admin', 'contractor', 'viewer'],
  },
  {
    path: '/daily-logs/create',
    permission: 'daily_logs.create',
    roles: ['admin', 'contractor'],
  },
  {
    path: '/daily-logs/edit',
    permission: 'daily_logs.edit',
    roles: ['admin', 'contractor'],
  }, // PMA Management
  {
    path: '/pmas',
    permission: 'pmas.view',
    roles: ['admin', 'contractor'],
  },
  {
    path: '/pma-management',
    permission: 'pmas.view',
    roles: ['admin', 'contractor'],
  },
  {
    path: '/pmas/create',
    permission: 'pmas.create',
    roles: ['admin', 'contractor'],
  },
  {
    path: '/pmas/edit',
    permission: 'pmas.edit',
    roles: ['admin', 'contractor'],
  },
  {
    path: '/pmas/approve',
    permission: 'pmas.approve',
    roles: ['admin'],
  },

  // Complaint Management
  {
    path: '/complaints',
    permission: 'complaints.view',
    roles: ['admin', 'contractor', 'viewer'],
  },
  {
    path: '/complaints/create',
    permission: 'complaints.create',
    roles: ['admin', 'contractor', 'viewer'],
  },
  {
    path: '/complaints/edit',
    permission: 'complaints.edit',
    roles: ['admin', 'contractor'],
  },

  // Blacklist Management
  { path: '/blacklist', permission: 'blacklist.view', roles: ['admin'] },
  {
    path: '/blacklist/create',
    permission: 'blacklist.create',
    roles: ['admin'],
  },
  {
    path: '/blacklist/edit',
    permission: 'blacklist.edit',
    roles: ['admin'],
  },

  // User Management
  { path: '/users', permission: 'users.view', roles: ['admin'] },
  { path: '/users/create', permission: 'users.create', roles: ['admin'] },
  { path: '/users/edit', permission: 'users.edit', roles: ['admin'] },
];

/**
 * Check if a user role has a specific permission
 */
export function hasPermission(
  userRole: UserRole | null,
  permission: Permission,
): boolean {
  if (!userRole) return false;
  return ROLE_PERMISSIONS[userRole]?.includes(permission) || false;
}

/**
 * Check if a user role can access a specific route
 */
export function canAccessRoute(
  userRole: UserRole | null,
  path: string,
): boolean {
  if (!userRole) return false;

  const routePermission = ROUTE_PERMISSIONS.find(
    (route) => route.path === path,
  );
  if (!routePermission) return false;

  return routePermission.roles.includes(userRole);
}

/**
 * Get all permissions for a user role
 */
export function getUserPermissions(userRole: UserRole | null): Permission[] {
  if (!userRole) return [];
  return ROLE_PERMISSIONS[userRole] || [];
}

/**
 * Check if a user role can perform an action on a resource
 */
export function canPerformAction(
  userRole: UserRole | null,
  resource: Resource,
  action: Action,
): boolean {
  const permission = `${resource}.${action}` as Permission;
  return hasPermission(userRole, permission);
}

/**
 * Filter accessible routes for a user role
 */
export function getAccessibleRoutes(
  userRole: UserRole | null,
): RoutePermission[] {
  if (!userRole) return [];
  return ROUTE_PERMISSIONS.filter((route) => route.roles.includes(userRole));
}

/**
 * Get role hierarchy level (higher number = more permissions)
 */
export function getRoleLevel(role: UserRole): number {
  const hierarchy = {
    viewer: 1, // formerly Client
    contractor: 2, // formerly Contractor
    admin: 3, // formerly JKR_PIC and JKR_Admin (combined)
  };
  return hierarchy[role] || 0;
}

/**
 * Check if user role has higher or equal permissions than required role
 */
export function hasRoleOrHigher(
  userRole: UserRole | null,
  requiredRole: UserRole,
): boolean {
  if (!userRole) return false;
  return getRoleLevel(userRole) >= getRoleLevel(requiredRole);
}
