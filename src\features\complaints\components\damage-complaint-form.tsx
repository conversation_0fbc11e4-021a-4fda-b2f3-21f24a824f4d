'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Upload, X, CalendarIcon, FileText } from 'lucide-react';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { CreateComplaintInput, createComplaintInputSchema } from '../schemas';
import {
  useCreateComplaint,
  useUpdateComplaint,
} from '../hooks/use-complaints-simple';
import { MANTRAP_LOCATIONS, AGENCY_CODES } from '@/lib/constants';
import { useAgencyTranslations } from '@/hooks/use-translations';

interface DamageComplaintFormProps {
  onSuccess: () => void;
  onCancel: () => void;
  editMode?: boolean;
  initialData?: Partial<CreateComplaintInput>;
  complaintId?: string;
}

export function DamageComplaintForm({
  onSuccess,
  onCancel,
  editMode = false,
  initialData,
  complaintId,
}: DamageComplaintFormProps) {
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const createComplaintMutation = useCreateComplaint();
  const updateComplaintMutation = useUpdateComplaint();
  const tAgency = useAgencyTranslations();

  const form = useForm<CreateComplaintInput>({
    resolver: zodResolver(createComplaintInputSchema),
    defaultValues: {
      email: initialData?.email || '',
      damageComplaintDate: initialData?.damageComplaintDate || new Date(),
      expectedCompletionDate: initialData?.expectedCompletionDate || new Date(),
      agency: initialData?.agency || '',
      contractorCompanyName: initialData?.contractorCompanyName || '',
      location: initialData?.location || '',
      description: initialData?.description || '',
      mantrapLocation: initialData?.mantrapLocation || '',
      contactNumber: initialData?.contactNumber || '',
      contractorEmail: initialData?.contractorEmail || '',
      contractorContactNumber: initialData?.contractorContactNumber || '',
      noPmaLif: initialData?.noPmaLif || '',
      actualCompletionDate: initialData?.actualCompletionDate || undefined,
      completionNotes: initialData?.completionNotes || '',
      status: (initialData?.status || 'pending') as
        | 'complete'
        | 'in progress'
        | 'pending',
      statusNotes: initialData?.statusNotes || '',
      contractorNotes: initialData?.contractorNotes || '',
    },
  });

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const validFiles = files.filter((file) => {
      const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB
      const isValidType = [
        'image/jpeg',
        'image/png',
        'image/jpg',
        'application/pdf',
      ].includes(file.type);
      return isValidSize && isValidType;
    });

    if (uploadedFiles.length + validFiles.length > 5) {
      alert('Maximum 5 files allowed');
      return;
    }

    setUploadedFiles((prev) => [...prev, ...validFiles]);
  };

  const removeFile = (index: number) => {
    setUploadedFiles((prev) => prev.filter((_, i) => i !== index));
  };
  const onSubmit = async (data: CreateComplaintInput) => {
    try {
      if (editMode && complaintId) {
        await updateComplaintMutation.mutateAsync({
          id: complaintId,
          data: {
            ...data,
            proofOfRepairFiles: uploadedFiles,
          },
        });
      } else {
        await createComplaintMutation.mutateAsync({
          ...data,
          proofOfRepairFiles: uploadedFiles,
        });
      }
      onSuccess();
    } catch (error) {
      console.error(
        editMode
          ? 'Failed to update complaint:'
          : 'Failed to create complaint:',
        error,
      );
    }
  };
  return (
    <form
      onSubmit={form.handleSubmit(onSubmit)}
      className="max-w-6xl mx-auto space-y-8"
    >
      {/* Contact Information */}
      <Card className="shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-semibold text-gray-900">
            Contact Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label
                htmlFor="email"
                className="text-sm font-medium text-gray-700"
              >
                Email Address *
              </Label>
              <Input
                id="email"
                type="email"
                {...form.register('email')}
                className={cn(
                  'h-11 px-3 py-2',
                  form.formState.errors.email &&
                    'border-red-500 focus-visible:ring-red-500',
                )}
                placeholder="Enter your email address"
              />
              {form.formState.errors.email && (
                <p className="text-sm text-red-500 mt-1">
                  {form.formState.errors.email.message}
                </p>
              )}
            </div>
            <div className="space-y-2">
              <Label
                htmlFor="contactNumber"
                className="text-sm font-medium text-gray-700"
              >
                Contact Number
              </Label>
              <Input
                id="contactNumber"
                {...form.register('contactNumber')}
                className="h-11 px-3 py-2"
                placeholder="Enter contact number (optional)"
              />
            </div>
          </div>
        </CardContent>
      </Card>
      {/* Damage Information */}
      <Card className="shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-semibold text-gray-900">
            Damage Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                Damage Complaint Date *
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full h-11 justify-start text-left font-normal px-3',
                      !form.watch('damageComplaintDate') &&
                        'text-muted-foreground',
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {form.watch('damageComplaintDate') ? (
                      format(form.watch('damageComplaintDate'), 'PPP')
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={form.watch('damageComplaintDate')}
                    onSelect={(date) =>
                      form.setValue('damageComplaintDate', date || new Date())
                    }
                  />
                </PopoverContent>
              </Popover>
              {form.formState.errors.damageComplaintDate && (
                <p className="text-sm text-red-500 mt-1">
                  {form.formState.errors.damageComplaintDate.message}
                </p>
              )}
            </div>
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                Expected Completion Date *
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      'w-full h-11 justify-start text-left font-normal px-3',
                      !form.watch('expectedCompletionDate') &&
                        'text-muted-foreground',
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {form.watch('expectedCompletionDate') ? (
                      format(form.watch('expectedCompletionDate'), 'PPP')
                    ) : (
                      <span>Pick a date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={form.watch('expectedCompletionDate')}
                    onSelect={(date) =>
                      form.setValue(
                        'expectedCompletionDate',
                        date || new Date(),
                      )
                    }
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {form.formState.errors.expectedCompletionDate && (
                <p className="text-sm text-red-500 mt-1">
                  {form.formState.errors.expectedCompletionDate.message}
                </p>
              )}
            </div>
          </div>
          <div className="space-y-2">
            <Label
              htmlFor="description"
              className="text-sm font-medium text-gray-700"
            >
              Description
            </Label>
            <Textarea
              id="description"
              {...form.register('description')}
              className="min-h-[100px] px-3 py-2 resize-none"
              placeholder="Describe the damage in detail..."
              rows={4}
            />
          </div>
        </CardContent>
      </Card>{' '}
      {/* Location Information */}
      <Card className="shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-semibold text-gray-900">
            Location Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label
              htmlFor="location"
              className="text-sm font-medium text-gray-700"
            >
              Location *
            </Label>
            <Input
              id="location"
              {...form.register('location')}
              placeholder="Enter specific location"
              className={cn(
                'h-11 px-3 py-2',
                form.formState.errors.location &&
                  'border-red-500 focus-visible:ring-red-500',
              )}
            />
            {form.formState.errors.location && (
              <p className="text-sm text-red-500 mt-1">
                {form.formState.errors.location.message}
              </p>
            )}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                Mantrap Location
              </Label>
              <Select
                onValueChange={(value) =>
                  form.setValue('mantrapLocation', value)
                }
              >
                <SelectTrigger className="h-11">
                  <SelectValue placeholder="Select mantrap location" />
                </SelectTrigger>
                <SelectContent>
                  {MANTRAP_LOCATIONS.map((location) => (
                    <SelectItem key={location} value={location}>
                      {location}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label
                htmlFor="noPmaLif"
                className="text-sm font-medium text-gray-700"
              >
                NO PMA LIF
              </Label>
              <Input
                id="noPmaLif"
                {...form.register('noPmaLif')}
                placeholder="Enter NO PMA LIF"
                className="h-11 px-3 py-2"
              />
            </div>
          </div>
        </CardContent>
      </Card>{' '}
      {/* Agency & Contractor Information */}
      <Card className="shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-semibold text-gray-900">
            Agency & Contractor Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                Agency *
              </Label>
              <Select onValueChange={(value) => form.setValue('agency', value)}>
                <SelectTrigger
                  className={cn(
                    'h-11',
                    form.formState.errors.agency &&
                      'border-red-500 focus:ring-red-500',
                  )}
                >
                  <SelectValue placeholder="Select agency" />
                </SelectTrigger>
                <SelectContent>
                  {AGENCY_CODES.map((agencyCode) => (
                    <SelectItem key={agencyCode} value={agencyCode}>
                      {tAgency(agencyCode)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {form.formState.errors.agency && (
                <p className="text-sm text-red-500 mt-1">
                  {form.formState.errors.agency.message}
                </p>
              )}
            </div>
            <div className="space-y-2">
              <Label
                htmlFor="contractorCompanyName"
                className="text-sm font-medium text-gray-700"
              >
                Contractor/Company Name *
              </Label>
              <Input
                id="contractorCompanyName"
                {...form.register('contractorCompanyName')}
                className={cn(
                  'h-11 px-3 py-2',
                  form.formState.errors.contractorCompanyName &&
                    'border-red-500 focus-visible:ring-red-500',
                )}
                placeholder="Enter contractor/company name"
              />
              {form.formState.errors.contractorCompanyName && (
                <p className="text-sm text-red-500 mt-1">
                  {form.formState.errors.contractorCompanyName.message}
                </p>
              )}
            </div>
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label
                htmlFor="contractorEmail"
                className="text-sm font-medium text-gray-700"
              >
                Contractor Email
              </Label>
              <Input
                id="contractorEmail"
                type="email"
                {...form.register('contractorEmail')}
                placeholder="Enter contractor email (optional)"
                className="h-11 px-3 py-2"
              />
            </div>
            <div className="space-y-2">
              <Label
                htmlFor="contractorContactNumber"
                className="text-sm font-medium text-gray-700"
              >
                Contractor Contact Number
              </Label>
              <Input
                id="contractorContactNumber"
                {...form.register('contractorContactNumber')}
                placeholder="Enter contractor contact number (optional)"
                className="h-11 px-3 py-2"
              />
            </div>
          </div>
        </CardContent>
      </Card>{' '}
      {/* Completion Information (Optional) */}
      <Card className="shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-semibold text-gray-900">
            Completion Information (Optional)
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">
              Actual Completion Date
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    'w-full h-11 justify-start text-left font-normal px-3',
                    !form.watch('actualCompletionDate') &&
                      'text-muted-foreground',
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {form.watch('actualCompletionDate') ? (
                    format(form.watch('actualCompletionDate')!, 'PPP')
                  ) : (
                    <span>Pick a date (optional)</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={form.watch('actualCompletionDate')}
                  onSelect={(date) =>
                    form.setValue('actualCompletionDate', date)
                  }
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          <div className="space-y-2">
            <Label
              htmlFor="completionNotes"
              className="text-sm font-medium text-gray-700"
            >
              Completion Notes
            </Label>
            <Textarea
              id="completionNotes"
              {...form.register('completionNotes')}
              placeholder="Add completion notes (optional)..."
              className="min-h-[100px] px-3 py-2 resize-none"
              rows={4}
            />
          </div>
        </CardContent>
      </Card>{' '}
      {/* File Upload */}
      <Card className="shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-semibold text-gray-900 flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Proof of Repair Files
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="border-2 border-dashed border-gray-300 hover:border-gray-400 transition-colors rounded-lg p-8 text-center bg-gray-50/50">
            <input
              type="file"
              id="file-upload"
              multiple
              accept="image/*,.pdf"
              onChange={handleFileUpload}
              className="hidden"
            />
            <label htmlFor="file-upload" className="cursor-pointer block">
              <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-700">
                  Click to upload or drag and drop
                </p>
                <p className="text-xs text-gray-500">
                  PNG, JPG, PDF up to 10MB each (max 5 files)
                </p>
              </div>
            </label>
          </div>

          {uploadedFiles.length > 0 && (
            <div className="space-y-3">
              <Label className="text-sm font-medium text-gray-700">
                Uploaded Files
              </Label>
              <div className="space-y-2">
                {uploadedFiles.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-3 p-3 border border-gray-200 rounded-lg bg-white hover:bg-gray-50 transition-colors"
                  >
                    <FileText className="h-5 w-5 text-blue-500 flex-shrink-0" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {file.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {(file.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(index)}
                      className="hover:bg-red-50 hover:text-red-600 transition-colors"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>{' '}
      <Separator className="my-8" />
      {/* Status Section (For Contractors) */}
      <Card className="shadow-sm">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-semibold text-gray-900">
            Complaint Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">
                Current Status
              </Label>{' '}
              <Select
                defaultValue="pending"
                onValueChange={(value) =>
                  form.setValue(
                    'status',
                    value as 'complete' | 'in progress' | 'pending',
                  )
                }
              >
                <SelectTrigger className="h-11">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>{' '}
                <SelectContent>
                  <SelectItem value="complete">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-green-500"></div>
                      Complete
                    </div>
                  </SelectItem>
                  <SelectItem value="in progress">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-yellow-500"></div>
                      In Progress
                    </div>
                  </SelectItem>
                  <SelectItem value="pending">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 rounded-full bg-red-500"></div>
                      Pending
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label
                htmlFor="statusNotes"
                className="text-sm font-medium text-gray-700"
              >
                Status Notes (Optional)
              </Label>
              <Input
                id="statusNotes"
                {...form.register('statusNotes')}
                placeholder="Add notes about the status change..."
                className="h-11 px-3 py-2"
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label
              htmlFor="contractorNotes"
              className="text-sm font-medium text-gray-700"
            >
              Contractor Notes
            </Label>
            <Textarea
              id="contractorNotes"
              {...form.register('contractorNotes')}
              placeholder="Add any additional contractor notes or updates..."
              className="min-h-[100px] px-3 py-2 resize-none"
              rows={4}
            />
          </div>
        </CardContent>
      </Card>
      <Separator className="my-8" />
      {/* Form Actions */}
      <div className="flex flex-col sm:flex-row justify-end gap-4 pt-6 pb-8">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          className="h-12 px-8 order-2 sm:order-1 min-w-[120px]"
        >
          Cancel
        </Button>{' '}
        <Button
          type="submit"
          disabled={
            editMode
              ? updateComplaintMutation.isPending
              : createComplaintMutation.isPending
          }
          className="h-12 px-8 order-1 sm:order-2 min-w-[160px]"
        >
          {(
            editMode
              ? updateComplaintMutation.isPending
              : createComplaintMutation.isPending
          ) ? (
            <>
              <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              {editMode ? 'Updating...' : 'Creating...'}
            </>
          ) : editMode ? (
            'Update Complaint'
          ) : (
            'Create Complaint'
          )}
        </Button>
      </div>
    </form>
  );
}
