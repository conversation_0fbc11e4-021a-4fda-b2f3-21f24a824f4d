/**
 * PMA Management Feature
 *
 * This feature handles PMA (Project Management Authority) assignment and management
 * including form creation, entry management, and submission workflows.
 */

// Export main components
export { AddPmaPage } from './components/add-pma-page';
export { PmaEntriesList } from './components/pma-entries-list';
export { PmaForm } from './components/pma-form';
export { PmaManagementPage } from './components/pma-management-page';

// Export hooks
export { usePmaManagement } from './hooks/use-pma-management';

// Export types
export type { PmaEntry, PmaFormData, PmaFormState } from './types/pma-types';

// Export schemas
export { pmaFormSchema, type PmaFormSchema } from './schemas/pma-schema';
