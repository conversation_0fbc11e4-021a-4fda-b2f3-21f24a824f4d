'use client';

import { DamageComplaintForm } from '@/features/complaints/components/damage-complaint-form';
import { useParams, useRouter } from 'next/navigation';

export default function EditComplaintPage() {
  const router = useRouter();
  const params = useParams();
  const complaintId = params.id as string;

  const handleSuccess = () => {
    // Redirect back to complaints list with success message
    router.push('/complaints?success=updated');
  };
  const handleCancel = () => {
    // Navigate to complaints dashboard
    router.push('/complaints');
  };

  // In a real app, you would fetch the complaint data here
  // For now, we'll pass empty initial data
  const initialData = {};

  return (
    <DamageComplaintForm
      onSuccess={handleSuccess}
      onCancel={handleCancel}
      editMode={true}
      complaintId={complaintId}
      initialData={initialData}
    />
  );
}
