import { createServerSupabaseClient } from '@/lib/supabase-server';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { projectId, userEmail, role } = await request.json();

    if (!projectId || !userEmail || !role) {
      return NextResponse.json(
        { error: 'Missing required fields: projectId, userEmail, role' },
        { status: 400 },
      );
    }

    // Create server Supabase client with access to cookies
    const supabase = await createServerSupabaseClient();

    // Get current user from session
    const { data: sessionData, error: sessionError } =
      await supabase.auth.getSession();

    if (sessionError || !sessionData.session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized: No valid session' },
        { status: 401 },
      );
    }

    const currentUserId = sessionData.session.user.id;

    // Check if current user has permission to invite (admin or owner of project)
    const { data: currentUserProject, error: permissionError } = await supabase
      .from('project_users')
      .select('role')
      .eq('project_id', projectId)
      .eq('user_id', currentUserId)
      .eq('status', 'accepted')
      .eq('is_active', true)
      .single();

    if (
      permissionError ||
      !currentUserProject ||
      currentUserProject.role !== 'admin'
    ) {
      return NextResponse.json(
        {
          error:
            'Unauthorized: You do not have permission to invite users to this project',
        },
        { status: 403 },
      );
    }

    // Check if user exists in the system
    const { data: existingUser, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('email', userEmail)
      .single();

    if (userError && userError.code !== 'PGRST116') {
      // PGRST116 is "not found" error
      console.error('Error checking user:', userError);
      return NextResponse.json(
        { error: 'Database error while checking user' },
        { status: 500 },
      );
    }

    if (!existingUser) {
      return NextResponse.json(
        {
          error: 'User not found',
          message:
            'Only existing users can be invited to projects. The user must register first.',
        },
        { status: 404 },
      );
    }

    // Check if user is already a member of this project
    const { data: existingMembership, error: membershipError } = await supabase
      .from('project_users')
      .select('status')
      .eq('project_id', projectId)
      .eq('user_id', existingUser.id)
      .eq('is_active', true)
      .single();

    if (membershipError && membershipError.code !== 'PGRST116') {
      console.error('Error checking membership:', membershipError);
      return NextResponse.json(
        { error: 'Database error while checking membership' },
        { status: 500 },
      );
    }

    if (existingMembership) {
      if (existingMembership.status === 'accepted') {
        return NextResponse.json(
          { error: 'User is already a member of this project' },
          { status: 409 },
        );
      } else if (existingMembership.status === 'invited') {
        return NextResponse.json(
          { error: 'User already has a pending invitation to this project' },
          { status: 409 },
        );
      }
    }

    // Create project_users record with status = 'invited'
    const { data: invitation, error: inviteError } = await supabase
      .from('project_users')
      .insert({
        project_id: projectId,
        user_id: existingUser.id,
        role: role,
        status: 'invited',
        assigned_date: new Date().toISOString().split('T')[0],
        is_active: true,
        created_by: currentUserId,
        created_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (inviteError) {
      console.error('Error creating invitation:', inviteError);
      return NextResponse.json(
        { error: 'Failed to create invitation' },
        { status: 500 },
      );
    }

    return NextResponse.json(
      {
        success: true,
        message: 'User invited successfully',
        invitation,
      },
      { status: 200 },
    );
  } catch (error) {
    console.error('Invite user error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 },
    );
  }
}
