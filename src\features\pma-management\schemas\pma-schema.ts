import { z } from 'zod';

export const pmaFormSchema = z.object({
  customer: z.string().min(1, 'Customer name is required'),
  agency: z.string().min(1, 'Agency name is required'),
  location: z.string().min(1, 'Location is required'),
  pmaNumber: z.string().min(1, 'PMA number is required'),
  type: z.string().min(1, 'Type is required'),
  supervisor: z.string().min(1, 'Supervisor name is required'),
  dateReceived: z.string().min(1, 'Date received is required'),
  assignCp: z.string().min(1, 'CP assignment is required'),
});

export type PmaFormSchema = z.infer<typeof pmaFormSchema>;
