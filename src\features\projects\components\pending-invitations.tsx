'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { CheckCircle, Clock, Mail, XCircle } from 'lucide-react';
import { useTranslations } from 'next-intl';
import {
  useAcceptInvitation,
  useDeclineInvitation,
  usePendingInvitations,
} from '../hooks/use-project-invitations';

/**
 * Component to display and manage pending project invitations
 */
export function PendingInvitations() {
  const t = useTranslations('pages.projects.invitations');
  const { data: invitations = [], isLoading } = usePendingInvitations();
  const acceptInvitation = useAcceptInvitation();
  const declineInvitation = useDeclineInvitation();

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            {t('title', { defaultValue: 'Pending Invitations' })}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (invitations.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            {t('title', { defaultValue: 'Pending Invitations' })}
          </CardTitle>
          <CardDescription>
            {t('noInvitations', {
              defaultValue: 'You have no pending project invitations.',
            })}
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  const handleAccept = (invitationId: string) => {
    acceptInvitation.mutate(invitationId);
  };

  const handleDecline = (invitationId: string) => {
    declineInvitation.mutate(invitationId);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mail className="h-5 w-5" />
          {t('title', { defaultValue: 'Pending Invitations' })}
          <Badge variant="secondary">{invitations.length}</Badge>
        </CardTitle>
        <CardDescription>
          {t('description', {
            defaultValue: 'Review and respond to project invitations.',
          })}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {invitations.map((invitation) => (
            <div
              key={invitation.id}
              className="flex items-center justify-between p-4 border rounded-lg"
            >
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h4 className="font-semibold">
                    {invitation.project?.name ||
                      t('unknownProject', { defaultValue: 'Unknown Project' })}
                  </h4>
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    {invitation.status}
                  </Badge>
                </div>
                <div className="text-sm text-muted-foreground mb-1">
                  {t('roleLabel', { defaultValue: 'Role' })}:
                  <Badge variant="secondary" className="ml-1">
                    {invitation.role}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  {t('projectCode', { defaultValue: 'Project Code' })}:{' '}
                  {invitation.project?.code || 'N/A'}
                </p>
                {invitation.project?.location && (
                  <p className="text-sm text-muted-foreground">
                    {t('location', { defaultValue: 'Location' })}:{' '}
                    {invitation.project.location}
                  </p>
                )}
              </div>
              <div className="flex items-center gap-2 ml-4">
                <Button
                  size="sm"
                  onClick={() => handleAccept(invitation.id)}
                  disabled={acceptInvitation.isPending}
                  className="flex items-center gap-1"
                >
                  <CheckCircle className="h-4 w-4" />
                  {t('accept', { defaultValue: 'Accept' })}
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleDecline(invitation.id)}
                  disabled={declineInvitation.isPending}
                  className="flex items-center gap-1"
                >
                  <XCircle className="h-4 w-4" />
                  {t('decline', { defaultValue: 'Decline' })}
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
